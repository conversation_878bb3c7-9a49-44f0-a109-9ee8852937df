import {Space} from 'antd';
import {Checkbox} from 'antd';
import {useState} from 'react';
import {useBoolean} from 'huse';
import {Button} from '@panda-design/components';
import {useSaveLogic, useLeaveLogic} from './hooks';
import {createHandlerWithNotConfirm} from './utils';

interface Props {
    nextLocation: string;
    onCancel: () => void;
    type: 'notSaved' | 'invalidForm';
    resetForm: () => void;
    onConfirmLeave?: () => void;
}

const Footer = ({onCancel, nextLocation, type, resetForm, onConfirmLeave}: Props) => {
    const [loading, {on, off}] = useBoolean();
    const [checked, setChecked] = useState(false);

    const save = useSaveLogic({on, off, nextLocation, onConfirmLeave});
    const handleLeave = useLeaveLogic({loading, checked, resetForm, onConfirmLeave, nextLocation});

    const handleCancel = createHandlerWithNotConfirm(onCancel, checked);
    const handleSave = createHandlerWithNotConfirm(save, checked);

    return (
        <Space>
            {type === 'notSaved' && (
                <Checkbox checked={checked} onChange={e => setChecked(e.target.checked)}>
                    不再提示
                </Checkbox>
            )}
            <Button onClick={handleCancel} loading>取消</Button>
            {type === 'notSaved' && (
                <Button type="primary" onClick={handleSave} loading>
                    保存并离开
                </Button>
            )}
            <Button type="primary" onClick={handleLeave} loading>
                继续离开
            </Button>
        </Space>
    );
};

export default Footer;
